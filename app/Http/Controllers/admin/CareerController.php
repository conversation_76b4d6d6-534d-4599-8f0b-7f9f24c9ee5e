<?php

namespace App\Http\Controllers\admin;

use App\HelperClass\AppHelper;
use App\Jobs\SendCareerEmail;
use App\Models\FailCertificate;
use App\Models\PlacementPartner;
use Carbon\Carbon;
use Illuminate\Http\Request;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Models\Career;
use Illuminate\Support\Facades\App;

class CareerController extends Controller
{
    public $careerTag;
    public function __construct()
    {
        $this->careerTag = ['career'];
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $records = Career::select('career.id', 'career.title', 'career.slug', 'career.salary',
            'career.deadline', 'career.status', 'career.created_at', 'career.updated_at',
            'pp.title as placement_partner_name')
            ->leftJoin('placement_partners as pp', 'pp.id', 'career.placement_partner_id')
            ->where(function ($query) use ($request) {

                if ($request->has('status')) {
                    $query->where('career.status', $request->get('status'));
                }
                if ($request->has('career_location')) {
                    $query->where('career.location', 'like', '%' . $request->get('career_location') . '%');
                }
                if ($request->has('placement_partner')) {
                    $query->where('pp.title', 'like', '%' . $request->get('placement_partner') . '%');
                }
                if ($request->has('type')) {
                    $query->where('career.type', $request->get('type'));
                }

                if ($request->has('title')) {
                    $query->Where('career.title', 'like', '%' . $request->get('title') . '%');
                }
                // if ($request->has('posted_date_from') && $request->has('posted_date_to')) {
                //     $query->whereBetween('postdate', [$request->get('posted_date_from'), $request->get('posted_date_to')]);
                // }
                if ($request->has('deadline_date_from') && $request->has('deadline_date_to')) {
                    $query->whereBetween('career.postdate', [$request->get('deadline_date_from'), $request->get('deadline_date_to')]);
                }
                if ($request->has('salary')) {
                    $query->where('career.salary', 'like', '%' . $request->get('salary') . '%');
                }
                if ($request->has('company')) {
                    $query->where('career.company', 'like', '%' . $request->get('company') . '%');
                }
                if ($request->has('type')) {
                    $query->Where('career.type', 'like', '%' . $request->get('type') . '%');
                }
                if ($request->has('exp')) {
                    $query->Where('career.experience', 'like', '%' . $request->get('exp') . '%');
                }
                if ($request->has('deadline')) {
                    $query->where('career.deadline', $request->get('deadline'));
                }
                if ($request->has('job_type')) {
                    $query->where('career.job_type', $request->get('job_type'));
                }
            })
            ->orderBy('career.id', 'desc')
            ->groupBy('career.id')
            ->paginate(20);
        $records->appends($request->all());
        $filters = $request->all();

        return view('admin.career.list', compact('records', 'filters'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
        $vars = array(
            'task' => 'Add',
        );
        $allCourse = \App\Models\Course::where('status', 1)->pluck('name', 'id');
        $placement_partners = PlacementPartner::select('id', 'title', 'type')->where('type', 'Placement Partner')->get();
        return view('admin.career.form', compact('vars', 'allCourse', 'placement_partners'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $rank = Career::count();
        $slug = $this->solveSlug($request->get('slug'), null);
        $data = $request->merge(['slug' => $slug])
            ->only(
                'title',
                'slug',
                'type',
                'experience',
                'detail',
                'location',
                'vacancy',
                'salary',
                //'qualification',
                //'specification',
                //'deadline',
                'description',
                'image',
                'iconclass',
                'status',
                'image_alt',
                'image_title',
                'company',
                'job_type',
                'job_level',
                'rank',
                'deadline',
                'restrict_apply',
                'related_course_id',
                'placement_partner_id'
            );

        $this->validate($request, [
            'title' => 'required',
            'slug' => 'required|unique:career,slug,:id',
            'job_type' => 'required',
            'deadline' => 'required',
            'related_course_id' => 'required',
            'placement_partner_id' => 'required',
            // 'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            //         'email' => 'required|email|max:128|unique:users,email,:id',

        ]);

        if ($request->image) {

            $imageName = time() . '.' . $request->image->getClientOriginalExtension();
            $request->image->move(public_path('uploads/career'), $imageName);
            $data['image'] = '/uploads/career/' . $imageName;
        }
        $data['rank'] = $rank + 1;


        //        $emails = json_decode(app(AppHelper::class)->getSiteConfigByKey('career_apply_form_emails'), 1);
        //        if ($emails) {
        //            foreach ($emails as $email) {
        //                dispatch(new SendCareerEmail($data, $email))->delay(Carbon::now()->addSeconds(3));
        //                //\Mail::to($email)->send(new \App\Mail\CourseEnquiry($data));
        //            }
        //        }

        $today = Carbon::now()->format('Y-m-d H:i:s');
        $data['postdate'] = $today;
        $data['created_at'] = $today;
        $data['updated_at'] = $today;
        $newdata = Career::create($data);
//        \Cache::tags(['career'])->flush();
        event(new \App\Events\CacheHandler($this->careerTag));

        $request->session()->flash('alert-success', 'Created Successfully!');

        if ($request->has('save-continue'))
            return redirect()->back();
        elseif ($request->has('save-edit'))
            return redirect()->route('career.edit', ($newdata->id));
        else
            return \Redirect::to('/admin/career');
    }

    public function addCourseCareer(Request $request)
    {
        $courseId = $request->get('course_id');
        $rank = Career::count();
        $slug = $this->solveSlug($request->get('slug'), null);
        $data = $request->merge(['slug' => $slug])
            ->only(
                'title',
                'slug',
                'type',
                'experience',
                'detail',
                'location',
                'vacancy',
                'salary',
                //'qualification',
                //'specification',
                //'description',
                //'deadline',
                'image',
                'iconclass',
                'status',
                'image_alt',
                'image_title',
                'company',
                'job_type',
                'job_level',
                'rank',
                'deadline',
                'restrict_apply',
                'related_course_id',
                'placement_partner_id'
            );

        $this->validate($request, [
            'title' => 'required',
            'slug' => 'required|unique:career,slug,:id',
            'job_type' => 'required',
            'deadline' => 'required',
            'related_course_id' => 'required',
            'placement_partner_id' => 'required',
            // 'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            //         'email' => 'required|email|max:128|unique:users,email,:id',

        ]);

        if ($request->image) {

            $imageName = time() . '.' . $request->image->getClientOriginalExtension();
            $request->image->move(public_path('uploads/career'), $imageName);
            $data['image'] = '/uploads/career/' . $imageName;
        }
        $data['rank'] = $rank + 1;

        $today = Carbon::now()->format('Y-m-d H:i:s');
        $data['postdate'] = $today;
        $data['created_at'] = $today;
        $data['updated_at'] = $today;
        $newdata = Career::create($data);

        // get all related course ids
        $all_related_courses_ids = Career::select('id', 'related_course_id')
            ->where('related_course_id', 'not like', 'null')
            ->where('career.deadline', '>=', date('Y-m-d'))
            ->pluck('related_course_id', 'id')->toArray();
        // get all the id of career which matches related_course_ids
        $matched_course_career_ids = [];
        foreach ($all_related_courses_ids as $key => $all_related_courses_id) {
            if(is_array($all_related_courses_id)) {
                if(in_array($courseId, $all_related_courses_id)) {
                    $matched_course_career_ids[] = $key;
                }
            }
        }
//        dd($all_related_courses_ids, $matched_course_career_ids);
        $data['courseCareer'] =  \DB::table('career')
            ->select('id', 'title', 'location', 'image', 'image_alt', 'image_title', 'job_type', 'postdate', 'slug', 'vacancy')
            ->whereIn('id', $matched_course_career_ids)
            ->orderBy('created_at','desc')
            ->limit('3')
            ->get();
        $response['html'] = view('admin.course.career.career-list', ['records' => $data['courseCareer']])->render();
        $response['status'] = true;
        event(new \App\Events\CacheHandler());
        return response()->json($response);
    }

    public function solveSlug($slug, $id)
    {
        $data = Career::select('slug')->where('slug', $slug);
        if(is_null($id)){
            $data = $data->first();
        }else{
            $data = $data->where('id','!=', $id)->first();
        }
        if(is_null($data)){
            return $slug;
        }else{
            $randSlug = $slug.'-'.rand(99,99999);
            return $this->checkSlug($randSlug, $id);
        }
    }

    public function checkSlug($randSlug, $id)
    {
        $data = Career::select('slug')->where('slug', $randSlug);
        if(is_null($id)){
            $data = $data->first();
        }else{
            $data = $data->where('id','!=', $id)->first();
        }
        if(is_null($data)){
            return $randSlug;
        }else{
            return $this->solveSlug($randSlug, $id);
        }
    }


    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $row = Career::find($id);
        $courseId = $row->pluck('related_course_id');
        $vars = array(
            'task' => 'Edit',
        );

        $placement_partners = PlacementPartner::select('id', 'title', 'type')->where('type', 'Placement Partner')->get();
        $allCourse = \App\Models\Course::where('status', 1)->pluck('name', 'id');

        return view('admin.career.form', compact('row', 'vars', 'allCourse', 'placement_partners'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // return $request->all();
        $slug = $this->solveSlug($request->get('slug'), $id);
        $data = $request->merge(['slug' => $slug])->only(
            'title',
            'slug',
            'type',
            'experience',
            'detail',
            'description',
            'location',
            'vacancy',
            'salary',
            'iconclass',
            'status',
            'image_alt',
            'image_title',
            'company',
            'job_type',
            'job_level',
            'deadline',
            'restrict_apply',
            'related_course_id',
            'placement_partner_id'

        );

        $row = Career::find($id);

        $this->validate($request, [
            'title' => 'required',
            'slug' => 'required|unique:career,slug,' . $id,
            'vacancy' => 'numeric|min:0|max:200',
            'job_type' => 'required',
            'deadline' => 'required',
            'related_course_id' => 'required',
            'placement_partner_id' => 'required',

            // 'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            //         'email' => 'required|email|max:128|unique:users,email,:id',

        ]);


        // echo '<pre>';
        // print_r($request->image);
        // die;
        if ($request->image) {
            // die('ok image found');
            $imageName = time() . '.' . $request->image->getClientOriginalExtension();
            $request->image->move(public_path('uploads/career'), $imageName);
            /*
            echo base_path();

// Path to the 'app' folder
echo app_path();

// Path to the 'public' folder
echo public_path();

//Path to the 'app/storage' folder
echo storage_path();
*/
            \File::delete(public_path('uploads/career/' . $row->image));
            $data['image'] = '/uploads/career/' . $imageName;
        }
        // die;

        // $data = $request->only('title', 'body');
        $today = Carbon::now()->format('Y-m-d H:i:s');
        $data['updated_at'] = $today;
        $row->update($data);
        event(new \App\Events\CacheHandler($this->careerTag));

//        \Cache::tags(['career'])->flush();
//        event(new \App\Events\CacheHandler());

        $request->session()->flash('alert-success', 'Updated Successfully!');

        //return \Redirect::route('admin.career.edit', array($row->id));
        if ($request->has('save-continue'))
            return redirect()->back();
        else
            return \Redirect::to('/admin/career');
        // return \Redirect::route('career.edit', array($row->id))->with('message','Success');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //

        // die('ok');

        $data = Career::find($id);

        $status = false;

        if ($data) {
            $image = $data->image;

            $status = $data->delete();
            // SuccessGallery::destroy();

            // $status = 1;

            if ($status) {
//                \Cache::tags(['career'])->flush();
                event(new \App\Events\DeleteRecordEvent($image));
            }
            event(new \App\Events\CacheHandler($this->careerTag));

        }

        if ($status) {
            \Session::flash('alert-success', 'Deleted Successfully!');
        } else {
            \Session::flash('alert-danger', 'Delete failed!');
        }

        return \Redirect::route('career.index');
    }

    public function duplicateToNew(Request $request, $id)
    {
        $duplicateJob = true;
        $row = Career::find($id);
        if(!$row)
            return redirect()->route('career.index');

        $vars = array(
            'task' => 'Edit',
        );

        $allCourse = \App\Models\Course::where('status', 1)->pluck('name', 'id');
        $placement_partners = PlacementPartner::select('id', 'title', 'type')->where('type', 'Placement Partner')->get();
        return view('admin.career.form', compact('duplicateJob', 'vars', 'row', 'allCourse', 'placement_partners'));
    }

    public function storeDuplicateCareer(Request $request, $id)
    {
        dd($request->all());
    }

    public function slug($title)
    {
        $sluglib = new \App\Libraries\SlugLibrary;

        $slug = $sluglib->makeClientSlug($title);

        return \Response::json([
            'slug' => $slug
        ]);
    }

    public function sorting()
    {
        $records = Career::select('id', 'slug', 'title', 'experience', 'salary', 'deadline', 'status')
            ->where('deadline', '>', \DB::raw('NOW()'))
            ->orderBy('rank', 'asc')
            ->get();

        return view('admin.career.sorting-list', compact('records'));
    }

    public function updateProcessStatus(Request $request)
    {
        $post = Career::find($request->get('id'));
        $process = $post->process === 0?1:0;
        $post->process = $process;
        $post->save();
        event(new \App\Events\CacheHandler($this->careerTag));

    }

    public function updateCareerNote(Request $request)
    {
        $post = Career::find($request->get('id'));
        $post->note = $request->get('note');
        $post->save();
    }

    public function ordering(Request $request)
    {
        $records = Career::select('id', 'rank')->get();

        foreach ($records as $record) {
            $record->timestamps = false; // To disable update_at field updation
            $id = $record->id;

            foreach ($request->order as $order) {
                if ($order['id'] == $id) {
                    $record->update(['rank' => $order['rank']]);
                }
            }
        }
        event(new \App\Events\CacheHandler($this->careerTag));

        return response('Update Successfully.', 200);
    }


    public function mapping()
    {
        // $affected = \DB::table('career')->update(array('placement_partner_id' => null));
        $records = Career::groupBy('company')
            ->orderBy('company', 'asc')
            ->get();
        $company_array = [];
        foreach ($records as $id => $company) {;
            $company_array[] = [
                'id' => $company->id,
                'title' => $company->company,
                'placement_partner_id' => $company->placement_partner_id
            ];
        }
        $placementPartners = PlacementPartner::where('type', 'placement partner')->orderBy('title')->pluck('title', 'id');
        return view('admin.career.mapper', compact('company_array', 'placementPartners'));
    }

    public function mappingUpdate(Request $request)
    {
        $d = [];
        foreach ($request->all()['mapping'] as $mapped_data) {
            if ($mapped_data['career'] && $mapped_data['placement_partner']) {

                if (!array_key_exists('process', $mapped_data)) continue;

                // create new placement partner from company details of career
                // this case matches when --- Create New Placement Partner --- is selected from mapping form
                if ($mapped_data['placement_partner'] == 'add_placement_partner') {
                    $careerMain = Career::find($mapped_data['career']);
                    $placement_partner = PlacementPartner::create([
                        'title' => $careerMain->company,
                        'slug' => str_slug($careerMain->company),
                        'status' => 0,
                        'type' => 'placement partner',
                        'publish_state' => 'pending_for_approval',
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ]);
                }

                $careerMain = Career::find($mapped_data['career']);
                $careerForCompany = Career::where('company', $careerMain->company)->get();
                foreach ($careerForCompany as $career) {
                    $career->placement_partner_id = isset($placement_partner) ? $placement_partner->id : $mapped_data['placement_partner'];
                    $career->save();
                }
            }
        }


        return redirect()->back();
    }
}
