<?php

use App\Models\HeroBar;
use App\Models\Offer;
use Foundation\Lib\Cache;

if(! function_exists('filter_icon')) {
    function filter_icon($increment, $image)
    {
       return str_replace($increment.'-', '' , $image);
    }
}
if (! function_exists('get_new_image_file_path')) {
    function get_new_image_file_path($full_path, string $dimension=null, $strict=true) {
        if(!$dimension) {
            return $full_path;
        } else {
            $ig = explode('/', $full_path);
            $image_name = $ig[count($ig) - 1];
            $newImagePath = str_replace($image_name, $dimension . '_' . $image_name, $full_path);
            if ($strict) {              // return default image if $strict true
                if (!file_exists(public_path($newImagePath))) {
                    return $full_path;
                }
            }
            return $newImagePath;
        }
    }
}

if (! function_exists('paginateCollection')) {
    function paginateCollection($collection, $perPage, $pageName = 'page', $fragment = null)
    {
        $currentPage = \Illuminate\Pagination\LengthAwarePaginator::resolveCurrentPage($pageName);
        $currentPageItems = $collection->slice(($currentPage - 1) * $perPage, $perPage);
        parse_str(request()->getQueryString(), $query);
        unset($query[$pageName]);
        $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $currentPageItems,
            $collection->count(),
            $perPage,
            $currentPage,
            [
                'pageName' => $pageName,
                'path' => \Illuminate\Pagination\LengthAwarePaginator::resolveCurrentPath(),
                'query' => $query,
                'fragment' => $fragment
            ]
        );
        return $paginator;
    }
}

if (! function_exists('getBlackListedWord')) {
    function getBlackListedWord()
    {
        return \Cache::remember('global-blacklisted-words', Cache::TIME_INTERVAL, function () {
            return \App\Models\Word::select('word')->pluck('word')->toArray();
        });
    }
}

ini_set('display_errors', '1');
date_default_timezone_set("Asia/Kathmandu");
if (! function_exists('generateHash')) {
    function generateHash($string)
    {
        // Try to locate certificate file
        if (!$cert_store = file_get_contents(public_path(env('CONNECTIPS_CREDITOR', 'connectips/creditor_live.pfx')))) {
            echo "Error: Unable to read the cert file\n";
            exit;
        }

        // Try to read certificate file
        if (openssl_pkcs12_read($cert_store, $cert_info, env("CONNECTIPS_CREDITOR_PASS", 67545))) {
            if ($private_key = openssl_pkey_get_private($cert_info['pkey'])) {
                $array = openssl_pkey_get_details($private_key);
                // print_r($array);
            }
        } else {
            echo "Error: Unable to read the cert store.\n";
            exit;
        }
        $hash = "";

        if (openssl_sign($string, $signature, $private_key, "sha256WithRSAEncryption")) {
            $hash = base64_encode($signature);
            openssl_free_key($private_key);
        } else {
            echo "Error: Unable openssl_sign";
            exit;
        }
        return $hash;
    }
}

/**
 * Return Theme Path
 */
if (!function_exists('getThemePath')) {
    function getThemePath(): array
    {
        return [
            'default' => [
                'path' => base_path('resources/themes/default'),
                'image' => 'assets'.DIRECTORY_SEPARATOR.'themes'.DIRECTORY_SEPARATOR.'default'.DIRECTORY_SEPARATOR.'uploads',
                'asset' => 'assets'.DIRECTORY_SEPARATOR.'themes'.DIRECTORY_SEPARATOR.'default'.DIRECTORY_SEPARATOR,
                'status' => 0,
            ],
            'new' => [
                'path' => base_path('resources/themes/new'),
                'image' => 'assets'.DIRECTORY_SEPARATOR.'themes'.DIRECTORY_SEPARATOR.'new'.DIRECTORY_SEPARATOR.'uploads',
                'asset' => 'assets'.DIRECTORY_SEPARATOR.'themes'.DIRECTORY_SEPARATOR.'new'.DIRECTORY_SEPARATOR,
                'status' => 1,
            ]
        ];
    }
}

/**
 * Return Theme Path
 */
if (!function_exists('getMobileThemePath')) {
    function getMobileThemePath(): array
    {
        return [

            'mobile' => [
                'path' => base_path('resources/themes/mobile'),
                'image' => 'assets'.DIRECTORY_SEPARATOR.'themes'.DIRECTORY_SEPARATOR.'mobile'.DIRECTORY_SEPARATOR.'uploads',
                'asset' => 'assets'.DIRECTORY_SEPARATOR.'themes'.DIRECTORY_SEPARATOR.'mobile'.DIRECTORY_SEPARATOR,
                'status' => 1,
            ],
        ];
    }
}

/**
 * Return active theme
 */
if (!function_exists('selectTheme')) {
    function selectTheme(){
        $themeName = null;
        $themePath = getThemePath();
        foreach ($themePath as $key => $theme) {
            if ($theme['status'] == '1') {
                $themeName = $key;
            }
        }
        return $themeName;
    }
}
/**
 * Return active theme
 */
if (!function_exists('selectMobileTheme')) {
    function selectMobileTheme(){
        $themeName = null;
        $themePath = getMobileThemePath();
        foreach ($themePath as $key => $theme) {
            if ($theme['status'] == '1') {
                $themeName = $key;
            }
        }
        return $themeName;
    }
}
/**
 * Return active theme
 */
if (!function_exists('themeExist')) {
    function themeExist($theme){
        $themeName = null;
        $themePath = getMobileThemePath();
        foreach ($themePath as $key => $theme) {
            if ($theme['status'] == '1') {
                $themeName = $key;
            }
        }
        return $themeName;
    }
}

// get assets version
if (!function_exists('get_assets_ver')) {
    function get_assets_ver(){
        if(is_file(storage_path('app/assets_version.txt'))) {
            $ver = file_get_contents(str_replace("\n","", storage_path('app/assets_version.txt')));
        } else {
            $ver = date('md').rand(500, 9999999);
        }

        return $ver;
    }
}

// save course view count in a file
if (!function_exists('set_course_view_count_in_file')) {
    /**
     * @param $course_id
     * @param $view_count
     * @return void
     */
    function set_course_view_count_in_file($course_id, $view_count_inc_by) {
        $dir = storage_path('app/course_view_update');

        if(!file_exists($dir)) {
            mkdir($dir, 0777, true);
        }

        try {
            $original_view_count = 0;
            if(\Storage::disk('local')->exists('course_view_update/'.$course_id)) {
                $original_view_count = \Storage::disk('local')->get('course_view_update/'.$course_id);
            }

            \Storage::disk('local')->put('course_view_update/'.$course_id, $original_view_count + $view_count_inc_by);
        } catch (Exception $exception) {
            \Log::emergency('Failed to save course view count in a file. Course: '.$course_id.' Count: '.$view_count_inc_by, [
                'message' => $exception->getMessage(),
            ]);
        }

    }
}

if (!function_exists('getCurrentThemePath')) {
    function getCurrentThemePath($asset_path){

        $web_path = null;
        $mobile_path = null;
        $themeWebPath = getThemePath();
        $themeMobilePath = getMobileThemePath();
        foreach ($themeWebPath as $key => $theme) {
            if ($theme['status'] == '1') {
                $web_path = $theme['asset'];
                $web_path .= $key.'/';
            }
        }
        foreach ($themeMobilePath as $key => $theme) {
            if ($theme['status'] == '1') {
                $mobile_path = $theme['asset'];
                $mobile_path .= 'new/';
            }
        }

        $web = $web_path;
        $mobile = $mobile_path;

        $device = getResourceAgent()->device();
        if($device == 'iPad'){
            $path = $web;
        }else{
            $path = getResourceAgent()->isDesktop() ? $web : $mobile;
        }
        if (!file_exists($mobile)) {
            $path = $web;
        }

        return url($path.$asset_path);
    }
}

function getResourceAgent()
{
    return new \Jenssegers\Agent\Agent();
}

if (!function_exists('get_current_herobar_offer')) {
    function get_current_herobar_offer($offerId){
        if (($offerId)) {
//            $slug = basename(parse_url($link, PHP_URL_PATH));
            $offer = \App\Models\Offer::where('id', $offerId)->first();
//            if (!$offer) {
//                $offer = HeroBar::where('button_link', $link)->first();
//                if ($offer) {
//                    $offer->end_date = date('Y-m-d', strtotime($offer->expire_date));
//                    $offer->start_date = date('Y-m-d', strtotime($offer->publish_date));
//                }
//            }
           
        } else {
            $offer = null;
        }
        return $offer;

    }
}
if (!function_exists('get_image_url')) {
    function get_image_url($folder,$file){

        if (file_exists(public_path($file))){
            return asset($file);
        }

        $cardDefaultImage = get_site_setting_by_key('card_default_image');
        $defaultImage = app(\App\HelperClass\AppHelper::class)->getCourseCardLazyImage($cardDefaultImage);
        if (!$file) {
            return $defaultImage;
        }

        $filePath = "uploads/$folder/$file";

        if (file_exists(public_path($filePath))) {
            return asset($filePath);
        }

        return $defaultImage;
    }
}

// 
if (!function_exists('getImageUrl')) {
    function getImageUrl($imagePath, $type = null)
    {
        if (file_exists(public_path($imagePath))) {
            return asset($imagePath);
        }

        if ($type == 'logo') {
            return asset('assets/images/default-logo.png');
        }

        if ($type == 'gallery') {
            return asset('assets/images/gallery-default.jpg');
        }

       return asset('assets/images/no-image.jpg');
    }
}

if (!function_exists('get_site_setting_by_key')) {
    function get_site_setting_by_key($key){
        $siteSetting = \Illuminate\Support\Facades\Cache::remember('get_site_setting_by_key'.$key, Cache::TIME_INTERVAL, function () use ($key) {
            return \App\Models\Setting::where('key', $key)->first();
        });

        return $siteSetting?->value;
    }
}

/**
 * Get the slug of a string
 */
if (!function_exists('str_slug')) {
    function str_slug($string, $separator = '-')
    {
        return \Illuminate\Support\Str::slug($string, $separator);
    }
}
