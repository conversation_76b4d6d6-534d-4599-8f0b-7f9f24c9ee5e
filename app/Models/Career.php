<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>vel\Scout\Searchable;

class Career extends Model
{
    use Searchable, HasFactory;
    public function searchableAs()
    {
        return 'Career';
    }
    protected $table = 'career';

    public $timestamps = false;

    protected $fillable = [
        'title',
        'slug',
        'type',
        'experience',
        'detail',
        'location',
        'vacancy',
        'salary',
        'qualification',
        'specification',
        'description',
        'deadline',
        'image',
        'iconclass',
        'status',
        'image_alt',
        'image_title',
        'postdate',
        'company',
        'job_type',
        'rank',
        'job_level',
        'restrict_apply',
        'related_course_id',
        'placement_partner_id',
        'created_at',
        'updated_at'
    ];
    protected $casts = ['related_course_id' => 'array'];

    /**
     * Get the Course that this Career belongs to
     */
    public function course()
    {
        return $this->belongsTo('App\Models\Course');
        // return $this->belongsTo('App\Models\Course', 'foreign_key'); // if foreign key is other than the <table>_id

    }
    public function placementPartner()
    {
        return $this->belongsTo(PlacementPartner::class);
    }

    public function scopeIsNotExpired($query)
    {
        $query->where('deadline', '>=', Carbon::now()->format('Y-m-d'));
    }

    public function scopeHasPlacementPartnerLinked($query)
    {
        $query->whereNotNull('placement_partner_id');
    }
}
