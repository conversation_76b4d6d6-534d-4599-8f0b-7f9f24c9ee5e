@import "../common.scss";

.job-detail--sm {
    .jobBanner-section--sm {
        .image-wrapper {
            position: relative;

            .image-box {
                position: absolute;
                -o-object-fit: cover;
                object-fit: cover;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: -1;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .top-buttons {
                padding: 16px;
                position: fixed;
                top: 0;
                display: flex;
                align-items: center;
                width: 100%;
                z-index: 9;

                &.sticky-background {
                    background-color: white;
                }

                .back-btn {
                    background-color: $white;
                    border-radius: 50%;
                    width: 44px;
                    height: 44px;
                    flex-shrink: 0;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    text-decoration: none;

                    iconify-icon {
                        color: $black-600;
                        font-size: 22px;
                    }
                }

            }

            .jobBanner-content {
                position: relative;
                height: 100%;
                width: 100%;
                padding: 120px 20px;
                text-align: center;
                color: #fff;

                .date {
                    font-size: $base-font;
                    font-weight: $fw-regular;
                    margin-bottom: 8px;

                    .bold-text {
                        font-weight: $fw-medium;
                    }
                }

                .job-title {
                    font-size: 30px;
                    font-weight: $fw-bold;
                    color: $white;
                    margin-bottom: 8px;
                    line-height: 1.3;
                }

                .location {
                    font-size: $base-font;
                    font-weight: $fw-regular;
                    margin-bottom: 8px;

                    a {
                        font-weight: $fw-medium;
                        color: $white;
                    }
                }

            }
        }
    }

    .jobDetails-wrapper {
        padding: 30px 0;
        background-color: #fff;
        border-radius: 30px 30px 0 0;
        margin-top: -30px;
        position: relative;
        padding-bottom: 80px;

        .job_info {
            display: flex;
            gap: 8px;
            align-items: center;
            margin-bottom: 32px;
            flex-wrap: wrap;

            &--box {
                display: flex;
                gap: 4px;
                align-items: center;
                font-size: $sm-base-font;
                font-weight: $fw-regular;
                color: #000;

                .icon {
                    width: 16px;
                    height: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        }

        .title {
            color: $black-800;
            font-size: 24px;
            font-weight: $fw-bold;
            margin-bottom: 16px;
        }

        .info {
            margin-bottom: 0;
            font-size: $base-font;
            font-weight: $fw-regular;
            color: $black-600;
        }

        .responsibilities-list {
            margin: 0;
            list-style: disc;
            padding: 0 0 0 16px;

            li {
                font-size: $base-font;

                margin-bottom: 4px;
                color: $black-600;
                font-weight: $fw-regular;
            }
        }

        .job-description {
            margin-bottom: 32px;
        }

        .job-responsibilities {
            margin-bottom: 32px;
        }

        .job-qualifications {
            margin-bottom: 32px;
        }

        .join-txt {
            font-size: $base-font;
            color: $black-600;
            font-weight: $fw-regular;
            margin-bottom: 32px;

            a {
                color: $darkblue-800;
                font-weight: $fw-medium;
            }
        }

    }

    .menu-btn {
        border-top: 1px solid #DEE2E6;
        position: fixed;
        bottom: 0;
        background-color: $white;
        padding: 20px 0;
        width: 100%;
        align-items: center;
        display: flex;
        justify-content: center;
        z-index: 99;

        .job-type-wrapper {
            display: flex;
            gap: 10px;

            iconify-icon {
                font-size: 30px;
                color: $black-400;

            }

            .job-type {
                font-size: 10px;
                color: $gray-600;
                font-weight: $fw-medium;
                margin-bottom: 0px;

                p {
                    font-size: $base-font;
                    color: $black-400;
                    font-weight: $fw-medium;
                    margin-bottom: 0;
                }
            }
        }
    }
}
