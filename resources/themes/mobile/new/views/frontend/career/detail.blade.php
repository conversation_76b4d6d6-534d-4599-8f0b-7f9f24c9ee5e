@extends('frontend.layouts.custom-layout')

@section('meta_title', $metadata['meta_title'])
@section('meta_keywords', $metadata['meta_keywords'])
@section('meta_description', $metadata['meta_description'])

@push('css')
    <link rel="stylesheet" href="{{ getCurrentThemePath('scss/job-details.css?ver='.get_assets_ver()) }}"/>
{{--    <link rel="stylesheet" href="{{ getCurrentThemePath('new-css/page/job-details.css?ver='.get_assets_ver()) }}"/>--}}
    <link rel="stylesheet" href="{{ getCurrentThemePath('css/intlTelInput.css') }}" type="text/css"/>

@endpush

@section('seo')
    <link rel="canonical" href="{{ \App\Libraries\AppHelper::getCurrentSiteLink(url()->current()) }}"/>
@endsection


@section('content')
    <div class="job-detail--sm">
        <div class="jobBanner-section--sm">
            <div class="image-wrapper">
                <div class="image-box">
                    <img src="{{ asset(getCurrentThemePath('images/image 40.png')) }}" alt="">
                </div>
                <div class="top-buttons" id="top_navigation">
                    @include('frontend.common.back_link', ['parentUrl' =>null])
                </div>
                <div class="jobBanner-content">
                    <p class="date">
                        Apply by <span class="bold-text"> {{ \Carbon\Carbon::parse($row->deadline)->format('jS F, Y') }}</span>

                    </p>
                    <h6 class="job-title">{{ $row->title }}</h6>
                    @if ($row->company || $row->placement_partner_name)
                    <div class="location">
                        at <a href="#">{{ $row->placement_partner_name ? $row->placement_partner_name : $row->company }}</a>
                    </div>
                    @endif
                </div>
            </div>

        </div>
        <div class="jobDetails-wrapper">
            <div class="container">
                <div class="job_info">
                    @if(!is_null($row->job_level))
                    <div class="job_info--box">
                        <div class="icon">
                            <iconify-icon icon="emojione-monotone:level-slider"></iconify-icon>
                        </div>
                        {{ isset(config('broadway.career_job_levels')[$row->job_level]) ? config('broadway.career_job_levels')[$row->job_level]:'Not Define' }}
                    </div>
                    @endif
                    @if(!is_null($row->salary))
                    <div class="job_info--box">
                        <div class="icon">
                            <iconify-icon icon="mdi:cash-multiple"></iconify-icon>
                        </div>
                        {{ $row->salary }}
                    </div>
                    @endif
                    @if(!is_null($row->location))
                    <div class="job_info--box">
                        <div class="icon">
                            <iconify-icon icon="ion:location-sharp"></iconify-icon>
                        </div>
                        {{ $row->location }}
                    </div>
                    @endif
                </div>
                <div class="section-padding pt-0">
                        {!! $row['detail'] !!}
                </div>
            </div>
        </div>
        <div class="menu-btn">
            <div class="container">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div class="job-type-wrapper">
                        @if(!is_null($row->job_type))
                        <iconify-icon icon="gridicons:briefcase"></iconify-icon>
                        <div class="job-type">
                            JOB TYPE
                            <p>{{ isset(config('broadway.career_job_types')[$row->job_type]) ? config('broadway.career_job_types')[$row->job_type]:'Not Define' }}</p>
                        </div>
                        @endif
                    </div>
                    @if ($jon_expired)
                        @if(!$row->restrict_apply)
                            <div class="enquiry-btn">
                                <a href="javascript:void(0);" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#exampleModal">
                                    Apply to this Role <i class="fa-solid fa-arrow-right"></i>
                                </a>
                            </div>
                        @endif
                    @endif

                </div>

            </div>
        </div>
        <div class=" modal w-100 h-100 fade resume_modal" id="exampleModal" tabindex="-1"
             aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                     <div class="resume-modalwrapper--sm">
                        <div class="container">
                            <div class="header-section">
                                <a class="back-btn" data-bs-dismiss="modal" aria-label="Close">
                                    <iconify-icon icon="mdi:arrow-left"></iconify-icon>
                                </a>
                                <h3 class="title">
                                    Apply for the job
                                </h3>
                            </div>
                            <div class="form-wrapper">
                                @include('frontend.common.form_validation_message_global')
                                {!! Form::open(['route'=>'frontend.career.post', 'class'=>'base-form form-vertical', 'name'=>'career-form',
                                'id' => 'career-form', 'files' => true]) !!}
                                <input type="hidden" name="career_id" id="career_id" value="{{ $row->id }}">
                                <input type="hidden" name="reflink" value="{{ url()->current() }}">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input_group">
                                            <label for="name" class="form-label">Your Name <span class="text-red-800">*</span></label>
                                            {!! Form::text(AppHelper::getFormFieldName('name'), null, [
                                            'class' => 'form-control form-control-sm br-6',
                                            'placeholder' => 'Your Name',
                                            'tabindex' => 1,
                                            'title' => 'Your name is required.',
                                        ]) !!}
                                            @include('frontend.common.form_validation_message_single', ['for' => 'name'])
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input_group">
                                            <label for="email_address" class="form-label">Contact Email <span class="text-red-800">*</span></label>

                                            {!! Form::email(AppHelper::getFormFieldName('email'), null, [
                                                'class' => 'form-control form-control-sm br-6',
                                                'title' => 'Please enter a valid email',
                                                'placeholder' => 'Your Email',
                                                'pattern' => "([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$)",
                                                'tabindex' => 2,
                                            ]) !!}
                                            @include('frontend.common.form_validation_message_single', [
                                                'for' => 'email',
                                            ])
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input_group">
                                            <label for="mobile" class="form-label">Contact Number<span class="text-red-800">*</span></label>

                                            <div class="d-flex gap-4">
                                                <div class="tel-select">
                                                    {!! Form::tel(AppHelper::getFormFieldName('mobile'), null, [
                                                    'class' => 'form-control br-6 form-control-sm mobileNum',
                                                    'title' => 'Mobile number is required.',
                                                    'id' => 'mobile',
                                                    'tabindex' => 3,
                                                ]) !!}
                                                    @include('frontend.common.form_validation_message_single', ['for' => 'mobile'])
                                                </div>
                                            </div>

                                        </div>
                                    </div>


                                    <div class="col-md-6">
                                        <div class="input_group">
                                            <label for="expertise" class="form-label">Portfolio Url</label>
                                            {!! Form::text(AppHelper::getFormFieldName('portfolio'), null, [
                                            'class' => 'form-control form-control-sm br-6',
                                            'placeholder' => 'Online Portfolio, eg: http://www.domain.com',
                                            'tabindex' => 6,
                                        ]) !!}
                                            @include('frontend.common.form_validation_message_single', [
                                                'for' => 'portfolio',
                                            ])
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <label for="upload_resume" class="form-label">Upload Resume
                                            <span class="text-red-800">*</span></label>
                                        <div class="file-upload__wrapper">
                                            <div class="file-upload__contents">
                                                <div id="drop-zone" class="custom-file-upload">
                                                    <div class="icon-wrapper"><iconify-icon icon="typcn:upload" class="upload-icon"></iconify-icon></div>
                                                    <p>Drag and drop files here</p>
                                                    <p>-- OR --</p>
                                                    <input type="file" tabindex="7" id="file-input" name="{{ AppHelper::getFormFieldName('resume') }}" accept=".doc, .pdf, .docx, .png, .jpg, .jpeg, .gif, .svg">
                                                    <label for="file-input" id="browse-button">Browse Files</label>
                                                </div>
                                                <small class="note">Supporting docs format: doc, pdf, docx, png, jpg, jpeg, gif, svg</small>
                                                @include('frontend.common.form_validation_message_single', ['for' => 'resume'])
                                                <p id="file-error" class="text-danger"></p>
                                                <p id="file-name"></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="input_group">
                                            <label for="description" class="form-label">About Yourself
                                                <span class="text-red-800">*</span></label>
                                            {!! Form::textarea(AppHelper::getFormFieldName('about_yourself'), null, [
                                               'class' => 'form-control br-6',
                                               'placeholder' => 'Your Expertise',
                                               'row' => 3,
                                               'tabindex' => 8,
                                           ]) !!}
                                            @include('frontend.common.form_validation_message_single', [
                                                'for' => 'about_yourself',
                                            ])
                                        </div>
                                    </div>

                                    <div class="col-md-12">
                                        <div class="input_group">
                                            <label class="switch">
                                                <input type="checkbox" id="flexSwitchCheckDefault">
                                                <span class="slider round"></span>
                                            </label>
                                            <label for="flexSwitchCheckDefault" class="form-label">Are you Broadway Infosys' Student?</label>

                                        </div>

                                    </div>

                                    <div class="col-md-12 certificateNumber" style="display: none;">
                                        <div class="input_group">
                                            <label for="description" class="form-label">Certificate Number</label>
                                            {!! Form::text('certificate_number', null, [
                                            'class' => 'form-control form-control-sm br-6',
                                            'placeholder' => 'Certificate Number',
                                            'tabindex' => 9,
                                        ]) !!}
                                        </div>
                                    </div>


                                </div>
                                @if(!env('DISABLE_INVISIBLE_CAPTCHA'))
                                    <div>
                                        <br>
                                        {!! app('captcha')->render() !!}
                                        @if ($errors->has('g-recaptcha-response'))
                                            <span class="help-block">
                                            <strong>{{ $errors->first('g-recaptcha-response') }}</strong>
                                        </span>
                                        @endif
                                    </div>
                                @endif
                                <div class="menu-btn">
                                    <div class="container">
                                        <button type="submit" id ="form__submit" class="btn btn-primary btn-md  upload-btn w-100">
                                            Apply Now
                                        </button>
                                    </div>
                                </div>

                                </form>
                            </div>
                        </div>

                     </div>


                </div>
            </div>
        </div>

    </div>
@endsection


@push('js')
    <script type="text/javascript" src='{{ getCurrentThemePath('new-js/instructor.js') }}'></script>
    <script type="text/javascript" src='{{ getCurrentThemePath('js/jquery.maskedinput.min.js') }}'></script>
    <script type="text/javascript" src='{{ getCurrentThemePath('js/intlTelInput.js') }}'></script>
    <script type="text/javascript" src='{{ getCurrentThemePath('js/jquery.validate.js') }}'></script>
    <script type="text/javascript" src='{{ getCurrentThemePath('js/additional-methods.min.js') }}'></script>
    <script type="text/javascript" src="{{ getCurrentThemePath('js/lazy.min.js') }}"></script>
    <script type="text/javascript" src="{{ getCurrentThemePath('js/common/lazy.js') }}"></script>
    @include('frontend.common.contact_no_scripts')
    @include('frontend.common.blacklisted_word_validation')
    <script type="text/javascript">
        $(document).ready(function () {

            $(document).on('click','#flexSwitchCheckDefault',function (){
                if ($(this).is(':checked')) {
                    $('.certificateNumber').show();
                } else {
                    $('.certificateNumber').hide();
                }
            })

            $('#career-form').validate({
                rules: {
                    '58963nm': {
                        required: true,
                        nameOnly: true,
                        maxlength: 100,
                        minlength: 3
                    },
                    '58963mb': "required",
                    '58963rs': {
                        required: true,
                        extension: "doc|pdf|docx|png|jpg|jpeg|gif|svg|",
                    },
                    '58963em':{
                        required: true,
                        email: true,
                        checkBroadwayEmail: true,
                        maxlength: 100
                    },
                    '58963ay': {
                        required: true,
                        maxLetters: 1000,
                        // checkBlackListedWords: true,
                        // checkLink: true,
                    },
                },
                messages: {
                    '58963ay': {
                        required: "This is a required field.",
                        checkBlackListedWords: "Unwanted Content",
                        checkLink: "Please Remove Link",
                    },
                    '58963nm': {
                        nameOnly: "Please enter a valid name.",
                        required: "This is a required field.",
                        maxlength:"Please enter no more than 100 characters.",
                        minlength:"Please enter at least 3 characters.",
                    },
                    '58963rs': {
                        required: "This is a required field.",
                    },
                    '58963mb': {
                        required: "This is a required field.",
                    },
                    '58963em': {
                        required: "This is a required field.",
                        email: "Please enter a valid email.",
                        checkBroadwayEmail: "Please Enter Your Own Valid Email Address.",
                        maxlength:"Please enter no more than 100 characters.",
                    },
                },
                @if(app()->environment() !== 'production')
                submitHandler: function(form) {
                    $('#form__submit').prop('disabled', true).html('<i class="fa fa-spinner fa-spin custom__placement"></i> Submitting Data');
                    form.submit();
                }
                @endif
            });

            // For google invisible recaptcha (form submit only recaptcha validation pass)
            @if(app()->environment() == 'production')
                _submitEvent = function() {
                if($('#career-form').valid()){
                    $('#form__submit').prop('disabled', true).html('<i class="fa fa-spinner fa-spin custom__placement"></i> Submitting Data');
                    $('#career-form').submit();
                }
            }
            @endif
        });

        $(document).ready(function () {
            $("#career-form").submit(function (e) {
                let isValid = true;

                // Get the file input
                let fileInput = $("#file-input")[0];
                let fileError = $("#file-error");
                let allowedExtensions = ["doc", "pdf", "docx", "png", "jpg", "jpeg", "gif", "svg"];
                let maxSize = 5 * 1024 * 1024; // 5MB limit

                // Validate file selection
                if (!fileInput.files.length) {
                    fileError.text("Please upload a Resume.").show();
                    isValid = false;
                } else {
                    let file = fileInput.files[0];
                    let fileExtension = file.name.split('.').pop().toLowerCase();
                    let fileSize = file.size;

                    // Validate file type
                    if (!allowedExtensions.includes(fileExtension)) {
                        fileError.text("Invalid file type. Allowed: " + allowedExtensions.join(", ")).show();
                        isValid = false;
                    }

                    // Validate file size
                    if (fileSize > maxSize) {
                        fileError.text("File size exceeds 5MB limit.").show();
                        isValid = false;
                    }
                }

                // Prevent form submission if validation fails
                if (!isValid) {
                    e.preventDefault();
                }
            });

            // Hide error message when a file is selected
            $("#file-input").change(function () {
                $("#file-error").hide();
            });
        });
    </script>

    @if ($errors->count() > 0)
        <script>
            $(function() {
                $('#exampleModal').modal('show');
            });
        </script>
    @endif
@endpush
