@import "../common.scss";

.job-banner-wrapper {
    position: relative;

    .overlay-content {
        position: relative;
        height: 100%;
        width: 100%;
        // background: linear-gradient(0deg, rgba(0, 71, 176, 0.7) 0%, rgba(0, 71, 176, 0.7) 100%);
        text-align: center;

        .job_apply-date {
            color: $white;
            font-size: $base-font;
            font-weight: $fw-regular;
            margin-bottom: 8px;

            span {
                font-weight: $fw-medium;
            }

        }

        .job_title {
            color: $white;
            font-size: 46px;
            font-weight: $fw-bold;
            margin-bottom: 8px;
        }

        .job_company-name {
            font-size: $base-font;
            color: $white;
            font-weight: $fw-regular;
            margin-bottom: 32px;

            a {
                font-weight: $fw-medium;
                color: $white;
            }
        }

        .apply-btn {
            background-color: #00c3ff;
            color: $white;

            &:hover {
                background-color: #00c3ff;
                color: $white;
            }
        }
    }

    .bg-image {
        img {
            position: absolute;
            object-fit: cover;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
    }
}

.job-card-wrapper {
    .job-card {
        border-radius: 8px;
        background: rgba(0, 195, 255, 0.12);
        text-align: center;
        padding: 16px;
        height: 100%;

        .icon {
            width: 30px;
            height: 30px;
            margin: 0 auto 8px auto;

            iconify-icon {
                color: $black-600;
                font-size: 30px;
            }
        }

        .title {
            font-size: $sm-base-font;
            color: $gray-600;
            font-weight: $fw-regular;
            margin-bottom: 2px;
        }

        .content {
            font-size: $base-font;
            font-weight: $fw-medium;
            color: $black-400;
            margin-bottom: 0;
        }
    }
}

.job-details-wrapper {
    .title {
        color: $black-800;
        font-size: 24px;
        font-weight: $fw-bold;
        margin-bottom: 16px;
    }

    .info {
        margin-bottom: 0;
        font-size: $base-font;
        font-weight: $fw-regular;
        color: $black-600;
    }

    .responsibilities-list {
        margin: 0;
        list-style: disc;
        padding: 0 0 0 16px;

        li {
            font-size: $base-font;
            margin-bottom: 4px;
            color: $black-600;
            font-weight: $fw-regular;
        }
    }

    .job-description {
        margin-bottom: 32px;
    }

    .job-responsibilities {
        margin-bottom: 32px;
    }

    .job-qualifications {
        margin-bottom: 32px;
    }
    .join-txt{
        font-size: $base-font;
        color: $black-600;
        font-weight: $fw-regular;
        margin-bottom: 32px;
        a{
            color: $darkblue-800;
            font-weight: $fw-medium;
        }
    }
}
