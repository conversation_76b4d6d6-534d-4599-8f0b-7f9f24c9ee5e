@extends('admin.layouts.layout')

@section('title', 'Career ' . $vars['task'] . '!')

@section('content')

    <!-- page content -->
    <div class="right_col" role="main">
        <div class="">

            <div class="clearfix"></div>
            <div class="row">
                <div class="col-md-12 col-sm-12 col-xs-12">
                    <div class="x_panel">
                        <div class="x_title">
                            <h2>{{ $vars['task'] }} Career
                                <small>Please enter details</small>
                            </h2>


                            <ul class="nav navbar-right panel_toolbox">
                                <li><a class="btn btn-success" href="{{ route('career.create') }}"><i class="fa fa-edit">
                                            Add</i></a>
                                </li>
                                <li class="dropdown">
                                    <a href="{{ route('career.index') }}" class="btn btn-primary"><i class="fa fa-list">
                                            List</i></a>
                                </li>

                            </ul>


                            <div class="clearfix"></div>
                        </div>
                        <div class="x_content">
                            <br />


                            @include('admin.layouts.message')


                            @if ($vars['task'] === 'Add')
                                <!-- <form id="demo-form2" data-parsley-validate class="form-horizontal form-label-left"> -->
                                {!! Form::open(['route' => 'career.store', 'class' => 'form-horizontal form-label-left', 'files' => true]) !!}
                            @elseif (isset($duplicateJob))
                                {!! Form::model($row, [
                                    'route' => ['career.store'],
                                    'class' => 'form-horizontal form-label-left',
                                    'files' => true,
                                ]) !!}
                            @elseif ($vars['task'] === 'Edit')
                                {!! Form::model($row, [
                                    'route' => ['career.update', $row->id],
                                    'method' => 'put',
                                    'class' => 'form-horizontal form-label-left',
                                    'files' => true,
                                ]) !!}
                            @endif

                            @if (isset($duplicateJob))
                                <div class="form-group required">
                                    <label for="middle-name" class="control-label col-md-3 col-sm-3 col-xs-12"></label>
                                    <div class="col-md-6 col-sm-6 col-xs-12">
                                        <h2>
                                            <i class="fa fa-clone"></i>
                                        <strong>Duplicating {{ $row->title }}</strong>
                                        </h2>
                                    </div>
                                </div>
                            @endif

                            <div class="form-group required">
                                <label for="middle-name" class="control-label col-md-3 col-sm-3 col-xs-12">Title</label>
                                <div class="col-md-6 col-sm-6 col-xs-12">

                                    @if (old('title'))
                                        <?php $title = old('title'); ?>
                                    @elseif(isset($row->title))
                                        <?php $title = $row->title; ?>
                                    @else
                                        <?php $title = ''; ?>
                                    @endif

                                    <input id="title" name="title" class="form-control col-md-7 col-xs-12" required
                                        type="text" value="{{ $title }}">
                                </div>
                            </div>

                            <div class="form-group required">
                                <label for="middle-name" class="control-label col-md-3 col-sm-3 col-xs-12">Slug</label>
                                <div class="col-md-6 col-sm-6 col-xs-12">

                                    @if (old('slug'))
                                        <?php $slug = old('slug'); ?>
                                    @elseif(isset($row->slug))
                                        <?php $slug = $row->slug; ?>
                                    @else
                                        <?php $slug = ''; ?>
                                    @endif

                                    <input id="slug" required value="{{ $slug }}"
                                        class="form-control col-md-7 col-xs-12" type="text" name="slug"
                                        placeholder="eg about-us">

                                    <input type="hidden" id="ajaxurl" name="ajaxurl"
                                        value="{{ URL::to('admin/career/slug') }}">
                                </div>
                            </div>

                            <div class="form-group required">
                                <label for="status" class="control-label col-md-3 col-sm-3 col-xs-12">Type </label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <select id="type" name="type" class="form-control" required>

                                        @if (isset($row->type))
                                            <option value="career"
                                                @if ($row->type == 'career') selected @else '' @endif>Career</option>
                                            <option value="in-house"
                                                @if ($row->type == 'in-house') selected @else '' @endif>In-house</option>
                                        @else
                                            <option value="career"
                                                @if (old('type') === 'career') selected @else '' @endif>Career</option>
                                            <option value="in-house"
                                                @if (old('type') === 'in-house') selected @else '' @endif>In-house
                                            </option>
                                        @endif

                                    </select>
                                </div>
                            </div>

                            <div class="form-group required">
                                <label class="control-label col-md-3 col-sm-3 col-xs-12" for="first-name">Related Course
                                </label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    {!! Form::select('related_course_id[]', $allCourse ?? [], $courseCategoriesID ?? null, [
                                        'class' => 'form-control js-example-basic-multiple',
                                        'multiple' => 'multiple',
                                        'id' => 'topclass',
                                    ]) !!}
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="middle-name" class="control-label col-md-3 col-sm-3 col-xs-12">Years of
                                    Experience</label>
                                <div class="col-md-6 col-sm-6 col-xs-12">

                                    @if (old('experience'))
                                        <?php $experience = old('experience'); ?>
                                    @elseif(isset($row->experience))
                                        <?php $experience = $row->experience; ?>
                                    @else
                                        <?php $experience = ''; ?>
                                    @endif

                                    <input id="experience" class="form-control col-md-7 col-xs-12" type="text"
                                        name="experience" value="{{ $experience }}" placeholder="eg php-mysql-training">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="middle-name" class="control-label col-md-3 col-sm-3 col-xs-12">Detail</label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <div class="x_content">
                                        @if (old('detail'))
                                            <?php $detail = old('detail'); ?>
                                        @elseif(isset($row->detail))
                                            <?php $detail = $row->detail; ?>
                                        @else
                                            <?php $detail = ''; ?>
                                        @endif
                                        <textarea name="detail" id="detail" class="ckeditor">{{ $detail }}</textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="middle-name" class="control-label col-md-3 col-sm-3 col-xs-12">Short Description</label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <div class="x_content">
                                        @if (old('description'))
                                            <?php $description = old('description'); ?>
                                        @elseif(isset($row->description))
                                            <?php $description = $row->description; ?>
                                        @else
                                            <?php $description = ''; ?>
                                        @endif
                                        <textarea name="description" id="description" class="ckeditor">{{ $description }}</textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-md-3 col-sm-3 col-xs-12" for="first-name">Number of
                                    Vacancies
                                    <!-- <span class="required">*</span> -->
                                </label>
                                <div class="col-md-6 col-sm-6 col-xs-12">

                                    @if (old('vacancy'))
                                        <?php $vacancy = old('vacancy'); ?>
                                    @elseif(isset($row->vacancy))
                                        <?php $vacancy = $row->vacancy; ?>
                                    @else
                                        <?php $vacancy = ''; ?>
                                    @endif

                                    <input type="text" id="vacancy" name="vacancy"
                                        class="form-control col-md-7 col-xs-12" value="{{ $vacancy }}">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-md-3 col-sm-3 col-xs-12" for="first-name">Offered Salary
                                    <span class="required" style="color: red;">*</span>
                                </label>
                                <div class="col-md-6 col-sm-6 col-xs-12">

                                    @if (old('salary'))
                                        <?php $salary = old('salary'); ?>
                                    @elseif(isset($row->salary))
                                        <?php $salary = $row->salary; ?>
                                    @else
                                        <?php $salary = ''; ?>
                                    @endif

                                    <input type="text" id="salary" name="salary"
                                        class="form-control col-md-7 col-xs-12" value="{{ $salary }}">
                                    <label for="">Do not leave it blank.</label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-md-3 col-sm-3 col-xs-12" for="first-name">Icon Class
                                    <!-- <span class="required">*</span> -->
                                </label>
                                <div class="col-md-6 col-sm-6 col-xs-12">

                                    @if (old('iconclass'))
                                        <?php $iconclass = old('iconclass'); ?>
                                    @elseif(isset($row->iconclass))
                                        <?php $iconclass = $row->iconclass; ?>
                                    @else
                                        <?php $iconclass = ''; ?>
                                    @endif

                                    <input type="text" id="iconclass" name="iconclass"
                                        class="form-control col-md-7 col-xs-12" value="{{ $iconclass }}">
                                </div>
                            </div>
                            <div class="form-group required">
                                <label class="control-label col-md-3 col-sm-3 col-xs-12" for="first-name">Deadline
                                    {{-- <span class="required">*</span> --}}
                                </label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    @if (old('deadline'))
                                        <?php $deadline = old('deadline'); ?>
                                    @elseif(isset($row->deadline))
                                        <?php $deadline = $row->deadline; ?>
                                    @else
                                        <?php $deadline = ''; ?>
                                    @endif
                                    <input type="text" id="datepicker" name="deadline" autocomplete="off"
                                        class="form-control col-md-7 col-xs-12" required value="{{ $deadline }}">
                                </div>
                            </div>

                            <div class="form-group required">
                                <label for="status" class="control-label col-md-3 col-sm-3 col-xs-12">Job Type
                                    {{-- <span class="required">*</span> --}}
                                </label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <select id="job_type" name="job_type" class="form-control" required>
                                        <option value="">Select Job Type</option>
                                        @if (old('job_type'))
                                            <?php $job_type = old('job_type'); ?>
                                        @elseif(isset($row->job_type))
                                            <?php $job_type = $row->job_type; ?>
                                        @else
                                            <?php $job_type = ''; ?>
                                        @endif
                                        @foreach (config('broadway.career_job_types') as $slug => $type)
                                            <option value="{{ $slug }}"
                                                {{ isset($row) && $job_type == $slug ? 'selected' : '' }}>
                                                {{ $type }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="status" class="control-label col-md-3 col-sm-3 col-xs-12">Job Level</label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <select id="job_level" name="job_level" class="form-control">
                                        <option value="">Select Job Type</option>
                                        @if (old('job_level'))
                                            <?php $job_level = old('job_level'); ?>
                                        @elseif(isset($row->job_level))
                                            <?php $job_level = $row->job_level; ?>
                                        @else
                                            <?php $job_level = ''; ?>
                                        @endif
                                        @foreach (config('broadway.career_job_levels') as $slug => $level)
                                            <option value="{{ $slug }}"
                                                {{ isset($row) && $job_level == $slug ? 'selected' : '' }}>
                                                {{ $level }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>


                            <div class="form-group">
                                <label for="status" class="control-label col-md-3 col-sm-3 col-xs-12">Status </label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <select id="status" name="status" class="form-control" required>

                                        @if (isset($row->status))
                                            <option value="1"
                                                @if ($row->status == 1) selected @else '' @endif>Active</option>
                                            <option value="0"
                                                @if ($row->status == 0) selected @else '' @endif>Inactive
                                            </option>
                                        @else
                                            <option value="1"
                                                @if (old('status') === '1') selected @else '' @endif>Active</option>
                                            <option value="0"
                                                @if (old('status') === '0') selected @else '' @endif>Inactive
                                            </option>
                                        @endif

                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="restrict_apply" class="control-label col-md-3 col-sm-3 col-xs-12">Restrict
                                    Apply
                                    <a href="#" data-placement="top" data-toggle="tooltip"
                                        title="Hide Apply button"><i class="fa fa-question-circle"></i></a>
                                </label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <select id="restrict_apply" name="restrict_apply" class="form-control" required>

                                        @if (isset($row->restrict_apply))
                                            <option value="1"
                                                @if ($row->restrict_apply == 1) selected @else '' @endif>Yes</option>
                                            <option value="0"
                                                @if ($row->restrict_apply == 0) selected @else '' @endif>No</option>
                                        @else
                                            <option value="1">Yes</option>
                                            <option value="0" selected>No</option>
                                        @endif

                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="status" class="control-label col-md-3 col-sm-3 col-xs-12">Placement
                                    Partner</label>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <select id="job_level" name="placement_partner_id" class="form-control select2">
                                        <option value="">Select Placement Partner</option>

                                        @foreach ($placement_partners as $key => $item)
                                            <option value="{{ $item->id }}"
                                                {{ isset($row) && $row->placement_partner_id == $item->id ? 'selected' : '' }}>
                                                {{ $item->title }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="ln_solid"></div>
                            <div class="form-group">
                                <div class="col-md-6 col-sm-6 col-xs-12 col-md-offset-3">
                                    <!-- <button type="submit" class="btn btn-primary">Cancel</button> -->
                                    @include('admin.career.includes.form-button')
                                </div>
                            </div>

                            {!! Form::close() !!}
                            <!-- </form> -->
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>
    <!-- /page content -->


    <!-- footer content -->
    @include('admin.layouts.footer.footer')
    <!-- /footer content -->

@endsection

@section('footer_scripts')
    @include('admin.layouts.footer.form_footer')
    <script>
        $('.select2').select2();
    </script>
@endsection

@section('header_scripts')
    @include('admin.layouts.header.form_header')
@endsection
