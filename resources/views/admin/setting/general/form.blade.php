@extends('admin.layouts.layout')

@section('title', 'Manage Site Content')

@push('css')
    <link href="{{ asset('admin-panel/assets/toastr/toastr.min.css') }}" rel="stylesheet">
    <style>
        .dropify-wrapper {
            height: 102px !important;
        }

        .dropify-wrapper p {
            font-size: 15px !important;
        }

        .file-icon {
            font-size: 25px !important;
        }

        .homepage-wrapper {
            padding: 10px 10px;
            box-shadow: 0px 2px 3px 0px;
            margin-bottom: 15px;
        }

        .wrapper-card {
            background-color: #f1f1f1;
            padding: 20px;
            margin-bottom: 20px;
        }

        .wrapper-card-h4 {
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #bbb;
        }
    </style>
@endpush

@section('content')

    <!-- page content -->
    <div class="right_col" role="main">
        <div class="">
            <div class="page-title">
                <div class="title_left">
                    <h3>General Section</h3>
                </div>
            </div>
            <div class="clearfix"></div>

            <div class="row">
                <div class="col-md-12 col-xs-12">
                    <div class="x_panel">


                        <div class="x_content" style="display: block;">

                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs" role="tablist" id="top-tab">
                                <li role="presentation">
                                    <a class="top-tab-button" href="#home" aria-controls="general" role="tab"
                                        data-toggle="tab">Home</a>
                                </li>
                                <li role="presentation" class="active">
                                    <a class="top-tab-button" href="#stat" aria-controls="general" role="tab"
                                        data-toggle="tab">Stat</a>
                                </li>
                                <li role="presentation">
                                    <a class="top-tab-button" href="#otherSection" aria-controls="setting" role="tab"
                                        data-toggle="tab">Other</a>
                                </li>
                            </ul>

                            <!-- Tab panes -->
                            <div class="tab-content" style="margin-top: 20px;">
                                <div role="tabpanel" class="tab-pane" id="home">
                                    @include('admin.setting.general.includes.home')
                                </div>
                                <div role="tabpanel" class="tab-pane active" id="stat">
                                    @include('admin.setting.general.includes.stat')
                                </div>
                                <div role="tabpanel" class="tab-pane" id="otherSection">
                                    @include('admin.setting.general.includes.other')
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /page content -->

    <!-- footer content -->
    @include('admin.layouts.footer.footer')
    <!-- /footer content -->
@endsection

@section('footer_scripts')
    @include('admin.layouts.footer.form_footer')
    <script src="{{ asset('admin-panel/assets/dropify/dropify.min.js') }}"></script>
    <script>
        var drEvent = $('.dropify').dropify();
        drEvent.on('dropify.beforeClear', function(event, element) {
            return confirm("Do you really want to delete \"" + element.element.id + "\" ?");
        });

        drEvent.on('dropify.afterClear', function(event, element) {
            $.ajax({
                url: '{{ route('site-setting.remove-file') }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    key: element.element.id,
                },
                success: function(response) {
                    if (response.code == 200) {
                        alert(response.msg);
                    }
                },
                error: function(error) {}
            });
        });
    </script>
@endsection

@section('header_scripts')
    @include('admin.layouts.header.form_header')
    <link href="{{ asset('admin-panel/assets/dropify/dropify.min.css') }}" rel="stylesheet">
@endsection
