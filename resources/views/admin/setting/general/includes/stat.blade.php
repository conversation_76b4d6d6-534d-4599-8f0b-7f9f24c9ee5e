{!! Form::open([
    'url' => route('setting.update-data'),
    'class' => 'form-horizontal form-label-left input_mask',
    'enctype' => 'multipart/form-data',
]) !!}

{{ csrf_field() }}

<div class="wrapper-card">
    <h4 class="wrapper-card-h4"> Career <code>(Metrics)</code> </h4>
    <div class="row">
        <div class="col-md-4">
            <div class="form-group">
                <label for="">Total Courses</label>

                <div class="row">
                    <div class="col-md-6">
                        <select name="content[total_courses_type]" class="form-control">
                            <option value="static">Static</option>
                            <option value="dynamic">Dynamic</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <input type="text" name="content[total_courses]" id="" value="{{ $data['total_courses'] ?? null }}" class="form-control" placeholder="Courses Count">
                    </div>
                </div>
                @if ($errors->has('total_courses'))
                    <label for="error">{{ $errors->first('total_courses') }}</label>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="form-group">
    <div class="col-md-12 col-sm-9 col-xs-12">
        <button type="button" class="btn btn-primary">Cancel</button>
        <button class="btn btn-primary" type="reset">Reset</button>
        <button type="submit" class="btn btn-success">Save</button>
    </div>
</div>

{!! Form::close() !!}
